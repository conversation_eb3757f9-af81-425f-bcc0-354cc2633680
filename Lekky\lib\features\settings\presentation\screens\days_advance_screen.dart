import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/integer_input_field.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Days in Advance settings screen
class DaysAdvanceScreen extends ConsumerWidget {
  /// Constructor
  const DaysAdvanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Custom banner with back arrow and title
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                    ),
                    child: SafeArea(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.arrow_back,
                                color: AppColors.getAppBarTextColor(
                                    'settings',
                                    Theme.of(context).brightness ==
                                        Brightness.dark),
                                size: 20,
                              ),
                              onPressed: () => Navigator.of(context).pop(),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Days in Advance',
                                style: TextStyle(
                                  color: AppColors.getAppBarTextColor(
                                      'settings',
                                      Theme.of(context).brightness ==
                                          Brightness.dark),
                                  fontSize: 28.0,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Days in advance card
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SettingsSectionHeader(
                                    title: 'Days in Advance',
                                    description:
                                        'Configure when you want to receive usage alerts.',
                                    icon: Icons.calendar_today,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Get notified this many days before you run out.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),
                                  _buildDaysInAdvanceInput(
                                      context, settings, ref),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  Widget _buildDaysInAdvanceInput(
      BuildContext context, SettingsState settings, WidgetRef ref) {
    return IntegerInputField(
      value: settings.daysInAdvance,
      onChanged: (value) {
        if (value != null) {
          ref.read(settingsProvider.notifier).updateDaysInAdvance(value);
        }
      },
      suffixText: 'days',
      labelText: 'Days in Advance',
      hintText: 'Enter days',
      minValue: 0,
      maxValue: 99,
    );
  }
}
