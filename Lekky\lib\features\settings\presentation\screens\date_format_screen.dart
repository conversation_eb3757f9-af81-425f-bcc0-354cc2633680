import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/shared/models/date_format.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/settings_radio.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Date Format settings screen
class DateFormatScreen extends ConsumerWidget {
  /// Constructor
  const DateFormatScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              // Convert string date format to enum
              DateFormat dateFormat =
                  DateFormat.fromString(settings.dateFormat);

              return Column(
                children: [
                  // Custom banner with back arrow and title
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                    ),
                    child: SafeArea(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.arrow_back,
                                color: AppColors.getAppBarTextColor(
                                    'settings',
                                    Theme.of(context).brightness ==
                                        Brightness.dark),
                                size: 20,
                              ),
                              onPressed: () => Navigator.of(context).pop(),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Date Format',
                                style: TextStyle(
                                  color: AppColors.getAppBarTextColor(
                                      'settings',
                                      Theme.of(context).brightness ==
                                          Brightness.dark),
                                  fontSize: 28.0,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date format card
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SettingsSectionHeader(
                                    title: 'Date Format',
                                    description:
                                        'Choose how dates will be displayed throughout the app.',
                                    icon: Icons.calendar_today,
                                  ),
                                  const SizedBox(height: 16),
                                  SettingsRadio<DateFormat>(
                                    title: 'Date Format',
                                    value: dateFormat,
                                    onChanged: (format) {
                                      ref
                                          .read(settingsProvider.notifier)
                                          .updateDateFormat(format);
                                    },
                                    options: const [
                                      SettingsRadioOption<DateFormat>(
                                        value: DateFormat.ddMmYyyy,
                                        title: 'DD-MM-YYYY',
                                        description: 'Example: 11-05-2025',
                                      ),
                                      SettingsRadioOption<DateFormat>(
                                        value: DateFormat.mmDdYyyy,
                                        title: 'MM-DD-YYYY',
                                        description: 'Example: 05-11-2025',
                                      ),
                                      SettingsRadioOption<DateFormat>(
                                        value: DateFormat.yyyyMmDd,
                                        title: 'YYYY-MM-DD',
                                        description: 'Example: 2025-05-11',
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
