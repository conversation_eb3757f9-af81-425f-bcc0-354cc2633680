import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../features/setup/presentation/widgets/appearance_settings_card.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/shared/models/theme_mode.dart';

/// Theme Mode settings screen
class ThemeModeScreen extends ConsumerWidget {
  /// Constructor
  const ThemeModeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // App Banner with "Theme Mode" title using gradient colors
                  Stack(
                    children: [
                      AppBanner(
                        message: 'Appearance',
                        gradientColors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                        textColor: AppColors.getAppBarTextColor('settings',
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                      // Back arrow positioned to the left of title on same row
                      Positioned(
                        top: 12,
                        left: 16,
                        child: IconButton(
                          icon: Icon(
                            Icons.arrow_back,
                            color: AppColors.getAppBarTextColor(
                                'settings',
                                Theme.of(context).brightness ==
                                    Brightness.dark),
                            size: 24,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Appearance settings card
                          AppearanceSettingsCard(
                            themeMode: settings.themeMode,
                            onThemeModeChanged: (mode) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateThemeMode(mode);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
