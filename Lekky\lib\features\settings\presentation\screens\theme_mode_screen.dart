import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../features/setup/presentation/widgets/appearance_settings_card.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/shared/models/theme_mode.dart';

/// Theme Mode settings screen
class ThemeModeScreen extends ConsumerWidget {
  /// Constructor
  const ThemeModeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Custom banner with back arrow and title
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                    ),
                    child: SafeArea(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 6.0,
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.arrow_back,
                                color: AppColors.getAppBarTextColor(
                                    'settings',
                                    Theme.of(context).brightness ==
                                        Brightness.dark),
                                size: 20,
                              ),
                              onPressed: () => Navigator.of(context).pop(),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Appearance',
                                style: TextStyle(
                                  color: AppColors.getAppBarTextColor(
                                      'settings',
                                      Theme.of(context).brightness ==
                                          Brightness.dark),
                                  fontSize: 28.0,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Appearance settings card
                          AppearanceSettingsCard(
                            themeMode: settings.themeMode,
                            onThemeModeChanged: (mode) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateThemeMode(mode);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
