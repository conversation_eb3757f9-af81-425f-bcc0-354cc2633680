import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../features/setup/presentation/widgets/radio_option.dart';

/// Language settings screen
class LanguageScreen extends ConsumerWidget {
  /// Constructor
  const LanguageScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // App Banner with "Language" title using gradient colors
                  Stack(
                    children: [
                      AppBanner(
                        message: 'Language',
                        gradientColors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                        textColor: AppColors.getAppBarTextColor('settings',
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                      // Back arrow positioned to the left of title within banner
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: SafeArea(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: 12.0,
                            ),
                            child: Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    Icons.arrow_back,
                                    color: AppColors.getAppBarTextColor(
                                        'settings',
                                        Theme.of(context).brightness ==
                                            Brightness.dark),
                                    size: 20,
                                  ),
                                  onPressed: () => Navigator.of(context).pop(),
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                                const SizedBox(width: 12),
                                // Spacer to push content to match banner layout
                                const Spacer(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Language section header
                                  Row(
                                    children: [
                                      const Icon(Icons.language,
                                          color: Colors.blue),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Language',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Current: ${settings.language}',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodySmall
                                                    ?.color,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Select your preferred language for the app interface.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),

                                  // Language options
                                  Column(
                                    children: [
                                      RadioOption<String>(
                                        value: 'English',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'English',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Spanish',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Spanish',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'French',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'French',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'German',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'German',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Italian',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Italian',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Portuguese',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Portuguese',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Russian',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Russian',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Chinese',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Chinese',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Japanese',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Japanese',
                                        icon: Icons.language,
                                      ),
                                      RadioOption<String>(
                                        value: 'Hindi',
                                        groupValue: settings.language,
                                        onChanged: (value) => ref
                                            .read(settingsProvider.notifier)
                                            .updateLanguage(value),
                                        title: 'Hindi',
                                        icon: Icons.language,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
