import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Notification Types settings screen
class NotificationTypesScreen extends ConsumerWidget {
  /// Constructor
  const NotificationTypesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading settings: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(settingsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (settings) => Column(
          children: [
            // App Banner with "Notification Types" title using gradient colors
            Stack(
              children: [
                AppBanner(
                  message: 'Notification Types',
                  gradientColors: AppColors.getSettingsMainCardGradient(
                      Theme.of(context).brightness == Brightness.dark),
                  textColor: AppColors.getAppBarTextColor('settings',
                      Theme.of(context).brightness == Brightness.dark),
                ),
                // Back arrow positioned in banner area
                Positioned(
                  top: 16,
                  left: 16,
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color: AppColors.getAppBarTextColor('settings',
                          Theme.of(context).brightness == Brightness.dark),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
              Card(
                margin: const EdgeInsets.all(8.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SettingsSectionHeader(
                        title: 'Notification Types',
                        description:
                            'Choose which notifications you want to receive.',
                        icon: Icons.notifications_active,
                      ),
                      const SizedBox(height: 16),

                      // Low Balance Alerts
                      SwitchListTile(
                        title: const Text('Low Balance Alerts'),
                        subtitle: const Text(
                            'Get notified when you have less than 24 hours of credit remaining'),
                        value: settings.lowBalanceAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateLowBalanceAlertsEnabled(value);
                        },
                      ),

                      const Divider(),

                      // Time to Top Up Alerts
                      SwitchListTile(
                        title: const Text('Time to Top Up Alerts'),
                        subtitle: const Text(
                            'Get notified when your alert threshold will be reached in your specified days in advance'),
                        value: settings.timeToTopUpAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateTimeToTopUpAlertsEnabled(value);
                        },
                      ),

                      const Divider(),

                      // Invalid Record Alerts
                      SwitchListTile(
                        title: const Text('Invalid Record Alerts'),
                        subtitle: const Text(
                            'Get notified when there\'s an issue with your meter readings.'),
                        value: settings.invalidRecordAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateInvalidRecordAlertsEnabled(value);
                        },
                      ),
                    ],
                  ),
                ),
              ),
                  ],
                ),
              ),
            ),
          ],
        ),
    );
  }
}
