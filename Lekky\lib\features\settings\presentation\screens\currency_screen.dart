import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../features/setup/presentation/widgets/radio_option.dart';

/// Currency settings screen
class CurrencyScreen extends ConsumerWidget {
  /// Constructor
  const CurrencyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // App Banner with "Currency" title using gradient colors
                  Stack(
                    children: [
                      AppBanner(
                        message: 'Currency',
                        gradientColors: AppColors.getSettingsMainCardGradient(
                            Theme.of(context).brightness == Brightness.dark),
                        textColor: AppColors.getAppBarTextColor('settings',
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                      // Back arrow positioned where icon would be
                      Positioned(
                        top: 16,
                        left: 16,
                        child: IconButton(
                          icon: Icon(
                            Icons.arrow_back,
                            color: AppColors.getAppBarTextColor(
                                'settings',
                                Theme.of(context).brightness ==
                                    Brightness.dark),
                            size: 20,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Currency section header
                                  Row(
                                    children: [
                                      const Icon(Icons.currency_exchange,
                                          color: Colors.blue),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Currency',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Current: ${settings.currency}',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodySmall
                                                    ?.color,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Select the currency for your meter readings.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),

                                  // Currency options
                                  _buildCurrencyOptions(settings, ref),

                                  const SizedBox(height: 8),
                                  Text(
                                    'Tip: Select the currency that matches your electricity bills.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  Widget _buildCurrencyOptions(SettingsState settings, WidgetRef ref) {
    // Define the currencies
    final currencies = [
      {'code': 'USD', 'symbol': '\$', 'name': 'US Dollar'},
      {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
      {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
      {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
      {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
      {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
      {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
      {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
      {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    ];

    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.sublist(midPoint);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currencyData) {
              return RadioOption<String>(
                value: currencyData['code']!,
                groupValue: settings.currency,
                onChanged: (value) => ref
                    .read(settingsProvider.notifier)
                    .updateCurrency(value, currencyData['symbol']!),
                title: '${currencyData['symbol']}${currencyData['code']}',
              );
            }).toList(),
          ),
        ),

        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currencyData) {
              return RadioOption<String>(
                value: currencyData['code']!,
                groupValue: settings.currency,
                onChanged: (value) => ref
                    .read(settingsProvider.notifier)
                    .updateCurrency(value, currencyData['symbol']!),
                title: '${currencyData['symbol']}${currencyData['code']}',
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
